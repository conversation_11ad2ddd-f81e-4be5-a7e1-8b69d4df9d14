{% for employer in all_employers %}
<div class="col-xl-4 col-lg-6 col-md-6 col-12">
  <div class="employer-card-clean">
    <a href="{{url_for('employer',employer_id=employer.employer_id)}}" class="employer-link-clean">
      <!-- Card Header -->
      <div class="employer-header-clean">
        <div class="employer-logo-clean">
          {% if employer.employer_logo_url %}
          <img
            src="{{ employer.employer_logo_url }}"
            class="company-logo-clean"
            alt="{{ employer.employer_name }}"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
          />
          {% endif %}
          <div class="company-logo-fallback-clean" {% if employer.employer_logo_url %}style="display: none"{% endif %}>
            {{ (employer.employer_name or 'C')[0]|upper }}
          </div>
        </div>

        <!-- Open Positions Badge -->
        {% if employer.open_positions > 0 %}
        <div class="positions-badge-clean">
          <i class="bi bi-briefcase me-1"></i>
          {{ employer.open_positions }} Open Positions
        </div>
        {% endif %}
      </div>

      <!-- Company Info -->
      <div class="employer-content-clean">
        <h3 class="company-name-clean">{{ employer.employer_name }}</h3>

        <!-- Company Details -->
        <div class="company-details-clean">
          {% if employer.employer_industry %}
          <div class="detail-row-clean">
            <i class="bi bi-diagram-3 me-2"></i>
            <span class="detail-text-clean">{{ employer.employer_industry }}</span>
          </div>
          {% endif %}

          {% if employer.employer_headcount %}
          <div class="detail-row-clean">
            <i class="bi bi-people me-2"></i>
            <span class="detail-text-clean">{{ employer.employer_headcount }}</span>
          </div>
          {% endif %}

          {% if employer.headquarter %}
          <div class="detail-row-clean">
            <i class="bi bi-geo-alt me-2"></i>
            <span class="detail-text-clean">{{ employer.headquarter }}</span>
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Clean Footer -->
      <div class="employer-footer-clean">
        <span class="view-text-clean">View Company</span>
        <i class="bi bi-arrow-right"></i>
      </div>
    </a>
  </div>
</div>
{% endfor %}
