{% extends "base.html" %}
{% block title %}Job Alerts{% endblock %}
{% block content %}

<div class="container mt-4">
  <div class="row justify-content-center">
    <div class="col-12 col-lg-8">
      
      <!-- Header -->
      <div class="text-center mb-5">
        <h1 class="h2 fw-bold text-primary mb-3">
          <i class="bi bi-bell me-2"></i>Job Alerts
        </h1>
        <p class="text-muted">Get notified when new jobs match your criteria</p>
      </div>

      <!-- Create Job Alert Form -->
      <div class="modern-card rounded-3 p-4 mb-5 shadow-sm">
        <h3 class="h4 fw-bold text-primary mb-4">
          <i class="bi bi-plus-circle me-2"></i>Create New Job Alert
        </h3>
        
        <form method="POST" action="{{ url_for('job_alerts') }}">
          <div class="row g-3">
            
            <!-- Email -->
            <div class="col-12">
              <div class="form-floating">
                <input 
                  type="email" 
                  class="form-control border-2" 
                  id="email" 
                  name="email" 
                  placeholder="<EMAIL>"
                  value="{{ user_email }}"
                  required
                />
                <label for="email">
                  <i class="bi bi-envelope me-2"></i>Email Address
                </label>
              </div>
            </div>

            <!-- Keywords -->
            <div class="col-12 col-md-6">
              <div class="form-floating">
                <input 
                  type="text" 
                  class="form-control border-2" 
                  id="keywords" 
                  name="keywords" 
                  placeholder="Python, React, Marketing"
                />
                <label for="keywords">
                  <i class="bi bi-tags me-2"></i>Keywords (comma-separated)
                </label>
              </div>
              <div class="form-text">e.g., Python, React, Marketing, Data Science</div>
            </div>

            <!-- Location -->
            <div class="col-12 col-md-6">
              <div class="form-floating">
                <input 
                  type="text" 
                  class="form-control border-2" 
                  id="location" 
                  name="location" 
                  placeholder="New York, Remote, Germany"
                />
                <label for="location">
                  <i class="bi bi-geo-alt me-2"></i>Preferred Location
                </label>
              </div>
              <div class="form-text">Leave empty for any location</div>
            </div>

            <!-- Remote Only -->
            <div class="col-12 col-md-6">
              <div class="form-check form-switch mt-3">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="remote_only" 
                  name="remote_only"
                >
                <label class="form-check-label fw-semibold" for="remote_only">
                  <i class="bi bi-laptop me-1"></i>Remote Jobs Only
                </label>
              </div>
            </div>

            <!-- Minimum Salary -->
            <div class="col-12 col-md-6">
              <div class="form-floating">
                <input 
                  type="number" 
                  class="form-control border-2" 
                  id="salary_min" 
                  name="salary_min" 
                  placeholder="50000"
                  min="0"
                />
                <label for="salary_min">
                  <i class="bi bi-currency-dollar me-2"></i>Minimum Salary (optional)
                </label>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="col-12">
              <button type="submit" class="btn btn-hero-primary btn-lg w-100 mt-3">
                <i class="bi bi-bell-fill me-2"></i>Create Job Alert
              </button>
            </div>

          </div>
        </form>
      </div>

      <!-- Existing Alerts -->
      {% if user_email and user_alerts %}
      <div class="modern-card rounded-3 p-4 shadow-sm">
        <h3 class="h4 fw-bold text-primary mb-4">
          <i class="bi bi-list-ul me-2"></i>Your Active Job Alerts
        </h3>
        
        <div class="row g-3">
          {% for alert in user_alerts %}
          <div class="col-12">
            <div class="alert-card p-3 border rounded-3 bg-light">
              <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                  <div class="d-flex align-items-center mb-2">
                    <i class="bi bi-bell text-primary me-2"></i>
                    <span class="fw-semibold">Alert #{{ alert.id }}</span>
                    <span class="badge bg-success ms-2">Active</span>
                  </div>
                  
                  <div class="alert-details">
                    {% if alert.keywords %}
                    <div class="mb-1">
                      <i class="bi bi-tags text-muted me-2"></i>
                      <strong>Keywords:</strong> {{ alert.keywords }}
                    </div>
                    {% endif %}
                    
                    {% if alert.location %}
                    <div class="mb-1">
                      <i class="bi bi-geo-alt text-muted me-2"></i>
                      <strong>Location:</strong> {{ alert.location }}
                    </div>
                    {% endif %}
                    
                    {% if alert.remote_only %}
                    <div class="mb-1">
                      <i class="bi bi-laptop text-muted me-2"></i>
                      <strong>Remote Only:</strong> Yes
                    </div>
                    {% endif %}
                    
                    {% if alert.salary_min %}
                    <div class="mb-1">
                      <i class="bi bi-currency-dollar text-muted me-2"></i>
                      <strong>Min Salary:</strong> ${{ "{:,}".format(alert.salary_min) }}
                    </div>
                    {% endif %}
                    
                    <div class="mb-1">
                      <i class="bi bi-calendar text-muted me-2"></i>
                      <strong>Created:</strong> {{ alert.created_at.strftime('%B %d, %Y') }}
                    </div>
                    
                    {% if alert.last_notification_sent %}
                    <div class="mb-1">
                      <i class="bi bi-envelope-check text-muted me-2"></i>
                      <strong>Last Notification:</strong> {{ alert.last_notification_sent.strftime('%B %d, %Y') }}
                    </div>
                    {% endif %}
                  </div>
                </div>
                
                <div class="ms-3">
                  <a 
                    href="{{ url_for('deactivate_alert', alert_id=alert.id, email=user_email) }}" 
                    class="btn btn-outline-danger btn-sm"
                    onclick="return confirm('Are you sure you want to deactivate this job alert?')"
                  >
                    <i class="bi bi-trash me-1"></i>Deactivate
                  </a>
                </div>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      {% elif user_email %}
      <div class="modern-card rounded-3 p-4 shadow-sm text-center">
        <i class="bi bi-envelope-at display-4 text-muted mb-3"></i>
        <h4 class="text-muted">Check your email for your job alerts!</h4>
        <p class="text-muted">You can always add more alerts above.</p>
      </div>
      {% endif %}

      <!-- Check Existing Alerts -->
      {% if not user_email %}
      <div class="modern-card rounded-3 p-4 shadow-sm">
        <h3 class="h4 fw-bold text-primary mb-3">
          <i class="bi bi-search me-2"></i>Check Your Existing Alerts
        </h3>
        <p class="text-muted mb-3">Enter your email to view and manage your existing job alerts.</p>
        
        <form method="GET" action="{{ url_for('job_alerts') }}">
          <div class="input-group">
            <input 
              type="email" 
              class="form-control" 
              name="email" 
              placeholder="Enter your email address"
              required
            />
            <button class="btn btn-outline-primary" type="submit">
              <i class="bi bi-search me-1"></i>View Alerts
            </button>
          </div>
        </form>
      </div>
      {% endif %}

      <!-- How It Works -->
      <div class="modern-card rounded-3 p-4 shadow-sm mt-4">
        <h3 class="h4 fw-bold text-primary mb-3">
          <i class="bi bi-info-circle me-2"></i>How Job Alerts Work
        </h3>
        
        <div class="row g-3">
          <div class="col-12 col-md-4">
            <div class="text-center">
              <i class="bi bi-bell-fill display-6 text-primary mb-2"></i>
              <h5 class="fw-semibold">1. Create Alert</h5>
              <p class="text-muted small">Set your job preferences and criteria</p>
            </div>
          </div>
          
          <div class="col-12 col-md-4">
            <div class="text-center">
              <i class="bi bi-search display-6 text-primary mb-2"></i>
              <h5 class="fw-semibold">2. We Search</h5>
              <p class="text-muted small">Our system checks for matching jobs daily</p>
            </div>
          </div>
          
          <div class="col-12 col-md-4">
            <div class="text-center">
              <i class="bi bi-envelope-fill display-6 text-primary mb-2"></i>
              <h5 class="fw-semibold">3. Get Notified</h5>
              <p class="text-muted small">Receive email notifications for new matches</p>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<style>
  .modern-card {
    background: white;
    border: 1px solid #e9ecef;
  }
  
  .alert-card {
    transition: all 0.2s ease;
  }
  
  .alert-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }
  
  .alert-details {
    font-size: 0.9rem;
  }
  
  @media (max-width: 768px) {
    .container {
      padding: 0 1rem;
    }
    
    .modern-card {
      margin: 0 0.5rem;
    }
  }
</style>

{% endblock %}
