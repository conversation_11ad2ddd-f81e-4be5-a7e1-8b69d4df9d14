#!/usr/bin/env python3
"""
Database migration script to add GDPR consent fields to existing tables.
This script adds consent tracking fields to feed_application and feed_talentpool tables.
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv(override=True)

def get_db_connection():
    """Get database connection using environment variables."""
    return psycopg2.connect(
        host=os.getenv("DATABASE_HOST"),
        database=os.getenv("DATABASE_NAME").strip("'"),
        user=os.getenv("DATABASE_USERNAME").strip("'"),
        password=os.getenv("DATABASE_PASSWORD").strip("'"),
        port=os.getenv("DATABASE_PORT").strip("'")
    )

def add_gdpr_consent_fields():
    """Add GDPR consent fields to the database tables."""
    try:
        conn = get_db_connection()
        cur = conn.cursor()
        
        print("Adding GDPR consent fields to database tables...")
        
        # Add consent fields to feed_application table
        print("Adding consent fields to feed_application table...")
        cur.execute("""
            ALTER TABLE feed_application 
            ADD COLUMN IF NOT EXISTS consent_future_opportunities BOOLEAN DEFAULT FALSE,
            ADD COLUMN IF NOT EXISTS consent_recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        """)
        
        # Add consent fields to feed_talentpool table
        print("Adding consent fields to feed_talentpool table...")
        cur.execute("""
            ALTER TABLE feed_talentpool 
            ADD COLUMN IF NOT EXISTS consent_future_opportunities BOOLEAN DEFAULT TRUE,
            ADD COLUMN IF NOT EXISTS consent_recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        """)
        
        # Create index for better performance on consent queries
        print("Creating indexes for consent fields...")
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_application_consent 
            ON feed_application(consent_future_opportunities);
        """)
        
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_talentpool_consent 
            ON feed_talentpool(consent_future_opportunities);
        """)
        
        conn.commit()
        print("✅ Successfully added GDPR consent fields to database!")
        
        # Verify the changes
        print("\nVerifying changes...")
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'feed_application' 
            AND column_name LIKE '%consent%'
            ORDER BY column_name;
        """)
        
        application_columns = cur.fetchall()
        if application_columns:
            print("feed_application consent columns:")
            for col in application_columns:
                print(f"  - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'feed_talentpool' 
            AND column_name LIKE '%consent%'
            ORDER BY column_name;
        """)
        
        talentpool_columns = cur.fetchall()
        if talentpool_columns:
            print("feed_talentpool consent columns:")
            for col in talentpool_columns:
                print(f"  - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error adding GDPR consent fields: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        raise

if __name__ == "__main__":
    add_gdpr_consent_fields()
